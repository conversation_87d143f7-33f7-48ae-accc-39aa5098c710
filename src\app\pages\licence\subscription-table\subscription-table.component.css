.stat-number {
  font-family: 'Montser<PERSON>', sans-serif;
  font-size: 2.25rem;
  font-weight: 800;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1;
}

.stat-label {
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

/* Enhanced Filters Section */
.enhanced-filters-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.filters-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.filters-title .material-icons {
  color: #10b981;
  font-size: 1.5rem;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 10px;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.clear-filters-btn:hover {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  transform: translateY(-1px);
}

.filters-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.search-section {
  width: 100%;
}

.enhanced-search-input {
  position: relative;
  max-width: 500px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 1.25rem;
  z-index: 2;
}

.search-input {
  flex: 1;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-family: 'Lato', sans-serif;
  font-size: 1rem;
  background: white;
  color: #1f2937;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.search-btn {
  position: static;
  margin-left: 0;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 50px;
}

.search-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: scale(1.05);
}

.search-btn:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
}

.search-btn .material-icons {
  font-size: 1.125rem;
}

.clear-search-btn {
  position: static;
  margin-left: 0.5rem;
  background: #f87171;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 50px;
}

.clear-search-btn:hover {
  background: #ef4444;
  transform: scale(1.05);
}

.clear-search-btn .material-icons {
  font-size: 1rem;
}

.filter-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.filter-label .material-icons {
  font-size: 1rem;
  color: #10b981;
}

.enhanced-select {
  padding: 0.875rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: white;
  color: #1f2937;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.enhanced-select:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

/* View Mode Toggle Buttons */
.view-mode-section {
  margin-bottom: 2rem;
}

.view-mode-toggle {
  display: flex;
  gap: 0.5rem;
  padding: 0.25rem;
  background: #f1f5f9;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  max-width: fit-content;
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: #64748b;
  border-radius: 10px;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.view-toggle-btn:hover {
  background: #e2e8f0;
  color: #1f2937;
}

.view-toggle-btn.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.view-toggle-btn .material-icons {
  font-size: 1.125rem;
}

/* Subscriptions Grid */
.subscriptions-grid {
  margin-bottom: 2rem;
}

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.grid-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.grid-title .material-icons {
  color: #10b981;
  font-size: 1.5rem;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.page-size-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #1f2937;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  cursor: pointer;
}

/* Cards Container */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem;
}

/* Enhanced Subscription Card */
.subscription-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.subscription-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.subscription-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.client-avatar {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #e2e8f0;
  flex-shrink: 0;
}

.client-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.client-fallback {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  color: #10b981;
  font-size: 1.125rem;
}

.client-details {
  flex: 1;
  min-width: 0;
}

.client-name {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: block;
}

.licence-name {
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: block;
}

/* Status Section */
.status-section {
  margin-bottom: 1.5rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge-paid {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #059669;
  border: 1px solid #bbf7d0;
}

.status-badge-pending {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  border: 1px solid #fde68a;
}

.status-badge-cancelled {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-badge-default {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.status-icon {
  font-size: 1rem;
}

/* Card Content */
.card-content {
  margin-bottom: 1.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Lato', sans-serif;
  font-size: 0.75rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.info-label .material-icons {
  font-size: 0.875rem;
  color: #10b981;
}

.info-value {
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
}

.price-value {
  color: #10b981;
  font-weight: 700;
  font-size: 1rem;
}

/* Card Footer */
.card-footer {
  padding-top: 1rem;
  border-top: 1px solid #f1f5f9;
}

.card-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

.card-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: 1px solid #d1d5db;
  background: #f9fafb;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  flex: 1;
  justify-content: center;
  text-decoration: none;
}

.card-action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #374151;
}

.card-action-btn .material-icons {
  font-size: 1rem;
}

.card-action-btn.modify-btn:hover {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #059669;
}

.card-action-btn.cancel-btn:hover {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.card-no-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.75rem;
  color: #9ca3af;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  font-style: italic;
}

/* Enhanced Table Styles - With Resizable Columns */
.table-strip {
  width: 100%;
  height: 5px;
  min-height: 5px;
  max-height: 12px;
  background: #10b981;
  border-radius: 8px 8px 0 0;
  margin-bottom: 0;
}

.table-container {
  background-color: white;
  border-radius: 0px 0px 8px 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  width: 100%;
  margin-bottom: 20px;
  padding: 8px 0 8px 0;
  overflow-x: auto;
  overflow-y: visible;
  position: relative;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.enhanced-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  font-family: 'Lato', sans-serif;
  font-size: 13px;
  min-width: 1000px;
}

.enhanced-table th, .enhanced-table td {
  padding: 8px 8px;
  text-align: left;
  border-bottom: 1px solid #eee;
  white-space: nowrap;
  font-family: 'Lato', sans-serif;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

/* Resizable Table Headers */
.enhanced-table th {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  color: #718096;
  font-size: 13px;
  background: #f8fafc;
  position: relative;
  user-select: none;
}

/* Column Resizer */
.column-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  z-index: 10;
  border-right: 2px solid transparent;
  transition: border-color 0.2s ease;
}

.column-resizer:hover,
.column-resizer.resizing {
  border-right-color: #10b981;
}

.column-resizer:hover::after,
.column-resizer.resizing::after {
  content: '';
  position: absolute;
  top: 0;
  right: -1px;
  width: 2px;
  height: 100%;
  background: #10b981;
  box-shadow: 0 0 4px rgba(16, 185, 129, 0.3);
}

/* Default column widths */
.enhanced-table th:nth-child(1), .enhanced-table td:nth-child(1) { min-width: 120px; width: 180px; }
.enhanced-table th:nth-child(2), .enhanced-table td:nth-child(2) { min-width: 100px; width: 140px; }
.enhanced-table th:nth-child(3), .enhanced-table td:nth-child(3) { min-width: 80px; width: 100px; }
.enhanced-table th:nth-child(4), .enhanced-table td:nth-child(4) { min-width: 80px; width: 100px; }
.enhanced-table th:nth-child(5), .enhanced-table td:nth-child(5) { min-width: 70px; width: 90px; }
.enhanced-table th:nth-child(6), .enhanced-table td:nth-child(6) { min-width: 70px; width: 90px; }
.enhanced-table th:nth-child(7), .enhanced-table td:nth-child(7) { min-width: 100px; width: 120px; }
.enhanced-table th:nth-child(8), .enhanced-table td:nth-child(8) { min-width: 110px; width: 130px; }

.enhanced-table th:last-child .column-resizer {
  display: none;
}

.enhanced-table.resizing {
  user-select: none;
}

.enhanced-table.resizing * {
  cursor: col-resize !important;
}

.enhanced-table th:not(:last-child) {
  border-right: 1px solid #e2e8f0;
}

.enhanced-table td:not(:last-child) {
  border-right: 1px solid #f1f5f9;
}

.column-resizer::before {
  content: '';
  position: absolute;
  top: 25%;
  right: 2px;
  width: 4px;
  height: 50%;
  background: linear-gradient(to bottom, transparent, #cbd5e1, transparent);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.enhanced-table th:hover .column-resizer::before,
.column-resizer.resizing::before {
  opacity: 1;
}

.enhanced-table tbody tr {
  cursor: pointer;
  transition: background 0.2s ease;
  position: relative;
}

.enhanced-table tbody tr:hover {
  background-color: #f3fdf7;
}

.enhanced-table tbody tr:last-child {
  border-bottom: none;
}

/* Table Cell Content */
.table-client-name {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-licence-name {
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  color: #4a5568;
  margin: 0;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-status-badge {
  padding: 4px 10px;
  border-radius: 10px;
  font-family: 'Lato', sans-serif;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
}

.table-status-badge.status-badge-paid {
  background-color: #e6f9f0;
  color: #10b981;
}

.table-status-badge.status-badge-pending {
  background-color: #fef2f2;
  color: #ef4444;
}

.table-status-badge.status-badge-cancelled {
  background-color: #fef2f2;
  color: #ef4444;
}

.table-status-badge.status-badge-default {
  background-color: #f1f5f9;
  color: #64748b;
}

.table-price {
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.875rem;
}

.table-date {
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  color: #4a5568;
  font-weight: 500;
}

.table-frequency {
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

/* Actions Cell Styles - 50% width for each button with proper hover effects */
.actions-cell {
  width: 130px;
  min-width: 130px;
}

.action-dropdown {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.table-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 0.4rem 0.5rem;
  border: 1px solid #d1d5db;
  background: #f9fafb;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  font-size: 0.75rem;
  text-decoration: none;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  width: 50%;
  box-sizing: border-box;
}

.table-action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #374151;
}

.table-action-btn .material-icons {
  font-size: 0.875rem;
}

.table-action-btn.modify-btn:hover {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #059669;
}

.table-action-btn.cancel-btn:hover {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

/* Swipe Indicators for Mobile */
.table-swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(16, 185, 129, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-family: 'Lato', sans-serif;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 5;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.table-swipe-indicator.left {
  left: 1rem;
}

.table-swipe-indicator.right {
  right: 1rem;
}

.table-container:hover .table-swipe-indicator {
  opacity: 1;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 16px;
  border: 2px dashed #e2e8f0;
}

.no-data-content {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.no-data-icon {
  font-size: 4rem;
  color: #cbd5e1;
  margin-bottom: 1rem;
}

.no-data-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  margin-bottom: 0.5rem;
}

.no-data-message {
  font-family: 'Lato', sans-serif;
  color: #64748b;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.create-subscription-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.create-subscription-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

/* Enhanced Pagination */
.enhanced-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.pagination-info {
  color: #64748b;
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f8fafc;
  border-color: #10b981;
  color: #10b981;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
  margin: 0 1rem;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
  font-family: 'Lato', sans-serif;
  font-weight: 500;
  padding: 0 0.75rem;
}

.page-btn:hover:not(:disabled):not(.ellipsis) {
  background: #f8fafc;
  border-color: #10b981;
  color: #10b981;
}

.page-btn.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  color: white;
}

.page-btn.ellipsis {
  border: none;
  background: none;
  cursor: default;
}

/* Enhanced Popups */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  padding: 1rem;
  box-sizing: border-box;
}

.enhanced-popup {
  background: white;
  border-radius: 20px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 0 2rem;
  margin-bottom: 1.5rem;
}

.popup-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.popup-title h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.popup-icon {
  font-size: 2rem;
}

.popup-icon.error-icon {
  color: #ef4444;
}

.close-popup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: #f8fafc;
  color: #64748b;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-popup-btn:hover {
  background: #e2e8f0;
  color: #1f2937;
}

.popup-content {
  padding: 0 2rem;
  margin-bottom: 2rem;
}

.popup-message p {
  font-family: 'Lato', sans-serif;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.popup-hint {
  font-family: 'Lato', sans-serif;
  color: #64748b;
  font-size: 0.875rem;
  font-style: italic;
}

.popup-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 0 2rem 2rem 2rem;
  border-top: 1px solid #f1f5f9;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
}

.popup-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.popup-btn.primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.popup-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

/* Success Notification */
.success-notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border-left: 4px solid #10b981;
  z-index: 2000;
  max-width: 400px;
  width: 90%;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.notification-text h4 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.notification-text p {
  font-family: 'Lato', sans-serif;
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.close-notification-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: #f0fdf4;
  color: #10b981;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.close-notification-btn:hover {
  background: #dcfce7;
  color: #059669;
}

/* Confirmation Popup Specific Styles */
.client-license-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  gap: 1rem;
  flex-wrap: wrap;
}

.summary-item:last-child {
  margin-bottom: 0;
  border-top: 2px solid #e2e8f0;
  padding-top: 1rem;
}

.label {
  font-weight: 600;
  color: #374151;
  font-size: 1rem;
  font-family: 'Lato', sans-serif;
  flex-shrink: 0;
}

.license-name {
  font-weight: 600;
  color: #1e293b;
  background: linear-gradient(135deg, #49b38e, #2c7744);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* No results row */
.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
  font-family: 'Lato', sans-serif;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .enhanced-container {
    padding: 1.5rem;
  }
  
  .cards-container {
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  }
  
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .enhanced-table {
    min-width: 900px;
  }
  
  .table-swipe-indicator {
    display: block;
  }
}

@media (max-width: 768px) {
  .enhanced-container {
    padding: 1rem;
  }
  
  .enhanced-header {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }
  
  .enhanced-title {
    font-size: 2rem;
  }
  
  .stats-dashboard {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .cards-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .filter-controls {
    grid-template-columns: 1fr;
  }
  
  .grid-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .enhanced-pagination {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .page-numbers {
    margin: 0;
  }

  .table-container {
    overflow-x: auto;
  }
  
  .enhanced-table {
    min-width: 800px;
  }
}

@media (max-width: 900px) {
  .enhanced-table th, .enhanced-table td {
    font-size: 12px;
    padding: 6px 4px;
  }
  .table-container {
    padding: 0;
  }
  .table-action-btn {
    padding: 0.3rem 0.4rem;
    font-size: 0.7rem;
    gap: 0.2rem;
  }
  .table-action-btn .material-icons {
    font-size: 0.75rem;
  }
  .action-dropdown {
    gap: 2px;
  }
}

@media (max-width: 600px) {
  .enhanced-table th, .enhanced-table td {
    font-size: 11px;
    padding: 4px 2px;
  }
  .table-container {
    padding: 0;
  }
  .table-action-btn {
    padding: 0.25rem 0.3rem;
    font-size: 0.65rem;
    gap: 0.1rem;
  }
  .table-action-btn .material-icons {
    font-size: 0.7rem;
  }
  .action-dropdown {
    gap: 1px;
    flex-direction: column;
  }
  
  .table-action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .card-action-btn {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }
  
  .table-swipe-indicator {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .enhanced-container {
    padding: 0.5rem;
  }
  
  .subscription-card {
    padding: 1rem;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .client-info {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .client-details {
    text-align: center;
  }
  
  .success-notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    left: 1rem;
    width: auto;
    max-width: none;
  }

  .view-mode-toggle {
    width: 100%;
  }
  
  .view-toggle-btn {
    flex: 1;
    justify-content: center;
  }
}

/* Hide swipe indicators on desktop */
@media (min-width: 1201px) {
  .table-swipe-indicator {
    display: none;
  }
}

/* Loading States */
.loading-card {
  background: #f8fafc;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Focus states for accessibility */
button:focus,
select:focus,
input:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .subscription-card {
    border: 2px solid #000;
  }
  
  .status-badge {
    border: 2px solid currentColor;
  }
  
  .enhanced-table {
    border: 2px solid #000;
  }
}/* Enhanced CSS with Table View Styles - Updated Font Usage */

:host {
  display: block;
  font-family: 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #1f2937;
  min-height: 100vh;
  line-height: 1.6;
}

/* Enhanced Container */
.enhanced-container {
  font-family: 'Lato', sans-serif;
  max-width: 98%;
  margin-left: 50px;
  padding: 24px;
  box-sizing: border-box;
}

/* Enhanced Header */
.enhanced-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 3rem;
  box-shadow: 0 20px 20px rgba(0, 0, 0, 0.08);
  padding: 2.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.header-content {
  flex: 1;
}

.enhanced-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  letter-spacing: -0.02em;
}

.enhanced-subtitle {
  font-family: 'Lato', sans-serif;
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 500;
  max-width: 600px;
  line-height: 1.7;
}

.header-actions {
  display: flex;
  align-items: center;
}

.add-subscription-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-family: 'Lato', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.add-subscription-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.add-subscription-btn .material-icons {
  font-size: 1.25rem;
}

/* Enhanced Statistics Dashboard */
.stats-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.stat-card.stat-pending::before {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card.stat-paid::before {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-card.stat-cancelled::before {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-card.stat-total::before {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.stat-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.stat-pending .stat-icon-wrapper {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.stat-cancelled .stat-icon-wrapper {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.stat-total .stat-icon-wrapper {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
}

.stat-icon {
  font-size: 1.75rem;
  color: #10b981;
}

.stat-pending .stat-icon {
  color: #f59e0b;
}

.stat-cancelled .stat-icon {
  color: #ef4444;
}

.stat-total .stat-icon {
  color: #6366f1;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.25rem;
  font-weight: 800;
  color: #1f2937;
  margin: 0 0 0.5rem
}

.card-action-btn.modify-btn.cancel-btn {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}