import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { MessageService } from 'primeng/api';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router,
    private messageService: MessageService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    // Check if user is authenticated
    if (!this.authService.isLoggedIn()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Authentication Required',
        detail: 'Please log in to access this page',
        life: 3000
      });
      this.router.navigate(['/login']);
      return false;
    }

    // Get required roles from route data
    const requiredRoles = route.data['roles'] as string[];
    
    // If no roles specified, allow access (authenticated users only)
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    // Check if user has any of the required roles
    if (!this.authService.hasAnyRole(requiredRoles)) {
      this.messageService.add({
        severity: 'error',
        summary: 'Access Denied',
        detail: 'You do not have permission to access this page',
        life: 5000
      });
      
      // Redirect based on user role
      // const currentUser = this.authService.getCurrentUser();
      // if (currentUser?.roles.includes('CLIENT')) {
      //   // Redirect CLIENT users to their organization details
      //   this.router.navigate(['/accueil']); // AccueilComponent will handle CLIENT redirect
      // } else {
      //   // Redirect other users to dashboard
      //   this.router.navigate(['/accueil']);
      // }
      
      return false;
    }

    return true;
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    return this.authService.getRoles().includes(role);
  }

  /**
   * Check if user is admin
   */
  isAdmin(): boolean {
    return this.hasRole('ADMINISTRATEUR') || this.hasRole('SUPERADMIN');
  }

  /**
   * Check if user is client
   */
  isClient(): boolean {
    return this.hasRole('CLIENT');
  }

  /**
   * Get user roles
   */
  getUserRoles(): string[] {
    return this.authService.getRoles();
  }
}
