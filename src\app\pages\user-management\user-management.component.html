<!-- User Management Component -->
<div class="user-management-container">
  <!-- Header Section -->
  <div class="header-card-container">
    <div class="header-section">
      <div class="page-title">
        <h1 class="title">
          <mat-icon class="title-icon">people</mat-icon>
          Gestion des Utilisateurs
        </h1>
      </div>
      <div class="actions">
        <button class="create-button" (click)="addNewUser()">
          <mat-icon class="action-icon">person_add</mat-icon>
          Ajouter un utilisateur
        </button>
      </div>
    </div>

    <div class="search-section">
      <div class="search-container">
        <input
          type="text"
          [(ngModel)]="searchParam"
          placeholder="Rechercher par nom, email ou rôle..."
          class="search-input"
          (keyup.enter)="applySearchFilter()"
        />
        <button class="search-button" (click)="applySearchFilter()">
          <mat-icon>search</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Users Section -->
  <div class="users-section">

    <!-- Loading Container -->
    <div class="loading-container" *ngIf="isLoadingUsers">
      <div class="spinner"></div>
      <p>Chargement des utilisateurs...</p>
    </div>

    <!-- Table View -->
    <div class="table-container" *ngIf="!isLoadingUsers && filteredUsers.length > 0">
      <app-generic-table
        [data]="paginatedUsers"
        [headers]="tableHeaders"
        [keys]="tableKeys"
        [actions]="tableActions"
        (actionTriggered)="handleTableAction($event)"
      >
      </app-generic-table>

      <!-- Pagination -->
      <div class="pagination-container">
        <mat-paginator
          [length]="totalUsers"
          [pageSize]="pageSize"
          [pageIndex]="currentPage"
          [pageSizeOptions]="[5, 10, 25, 50]"
          (page)="onPageChange($event)"
          aria-label="Select page"
        >
        </mat-paginator>
      </div>
    </div>

    <!-- No Users Message -->
    <div class="no-users-message" *ngIf="!isLoadingUsers && filteredUsers.length === 0">
      <i class="material-icons">people_outline</i>
      <p *ngIf="!hasSearchFilter">Aucun utilisateur trouvé. Commencez par créer votre premier utilisateur.</p>
      <p *ngIf="hasSearchFilter">Aucun résultat pour votre recherche "{{ searchParam }}"</p>
    </div>
  </div>
</div>

<!-- User Form Popup -->
<!-- User Form Popup -->
<div class="popup-overlay" *ngIf="showUserForm" (click)="closeUserForm()">
  <div class="popup-form" (click)="$event.stopPropagation()">
    <div class="popup-header">
      <h3>
        <mat-icon>{{ isEditMode ? "edit" : "person_add" }}</mat-icon>
        {{ isEditMode ? "Modifier l'Utilisateur" : "Ajouter un Utilisateur" }}
      </h3>
      <button class="close-btn" (click)="closeUserForm()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <form [formGroup]="userForm" (ngSubmit)="submitUserForm()" class="site-form">
      <!-- Validation errors section -->
      <div class="validation-errors" *ngIf="showValidationErrors && userForm.invalid">
        <div class="validation-errors-title">
          <mat-icon>error_outline</mat-icon>
          Erreurs de validation
        </div>
        <ul class="validation-errors-list">
          <li *ngIf="userForm.get('userName')?.invalid">
            <mat-icon>error</mat-icon>
            Le nom d'utilisateur est requis (minimum 3 caractères)
          </li>
          <li *ngIf="userForm.get('email')?.invalid">
            <mat-icon>error</mat-icon>
            Un email valide est requis
          </li>
          <li *ngIf="userForm.get('phoneNumber')?.invalid">
            <mat-icon>error</mat-icon>
            Le numéro de téléphone est requis
          </li>
          <li *ngIf="userForm.get('roles')?.invalid">
            <mat-icon>error</mat-icon>
            Le rôle est requis
          </li>
          <li *ngIf="userForm.get('newPassword')?.invalid && !isEditMode">
            <mat-icon>error</mat-icon>
            Le mot de passe est requis (minimum 10 caractères)
          </li>
          <li *ngIf="userForm.get('confirmPassword')?.invalid && !isEditMode">
            <mat-icon>error</mat-icon>
            La confirmation du mot de passe est requise
          </li>
          <li *ngIf="userForm.hasError('passwordMismatch') && !isEditMode">
            <mat-icon>error</mat-icon>
            Les mots de passe ne correspondent pas
          </li>
        </ul>
      </div>

      <div class="form-grid">
        <!-- Username Field -->
        <div class="form-group">
          <label for="userName">Nom d'utilisateur <span class="required">*</span></label>
          <input
            id="userName"
            type="text"
            formControlName="userName"
            placeholder="Entrez le nom d'utilisateur"
            required
          />
          <div class="error-message"
               *ngIf="showValidationErrors && userForm.get('userName')?.invalid">
            Le nom d'utilisateur est requis (minimum 3 caractères)
          </div>
        </div>

        <!-- Email Field -->
        <div class="form-group">
          <label for="email">Adresse Email <span class="required">*</span></label>
          <input
            id="email"
            type="email"
            formControlName="email"
            placeholder="<EMAIL>"
            required
          />
          <div class="error-message"
               *ngIf="showValidationErrors && userForm.get('email')?.invalid">
            Un email valide est requis
          </div>
        </div>

        <!-- Phone Field -->
        <div class="form-group">
          <label for="phoneNumber">Téléphone <span class="required">*</span></label>
          <input
            id="phoneNumber"
            type="tel"
            formControlName="phoneNumber"
            placeholder="+33 1 23 45 67 89"
            required
          />
          <div class="error-message"
               *ngIf="showValidationErrors && userForm.get('phoneNumber')?.invalid">
            Le numéro de téléphone est requis
          </div>
        </div>

        <!-- Role Field -->
        <div class="form-group">
          <label for="roles">Rôle <span class="required">*</span></label>
          <select id="roles" formControlName="roles" required>
            <option value="">Sélectionnez un rôle</option>
            <option *ngFor="let role of userRoles" [value]="role.value">
              {{ role.label }}
            </option>
          </select>
          <div class="error-message"
               *ngIf="showValidationErrors && userForm.get('roles')?.invalid">
            Le rôle est requis
          </div>
        </div>

        <!-- Password Fields (only for create mode) -->
        <div class="form-group" *ngIf="!isEditMode">
          <label for="newPassword">Mot de passe <span class="required">*</span></label>
          <input
            id="newPassword"
            type="password"
            formControlName="newPassword"
            placeholder="Minimum 10 caractères"
            required
          />
          <div class="error-message"
               *ngIf="showValidationErrors && userForm.get('newPassword')?.invalid">
            Le mot de passe est requis (minimum 6 caractères)
          </div>
        </div>

        <div class="form-group" *ngIf="!isEditMode">
          <label for="confirmPassword">Confirmer le mot de passe <span class="required">*</span></label>
          <input
            id="confirmPassword"
            type="password"
            formControlName="confirmPassword"
            placeholder="Répétez le mot de passe"
            required
          />
          <div class="error-message"
               *ngIf="showValidationErrors && (userForm.get('confirmPassword')?.invalid || userForm.hasError('passwordMismatch'))">
            <span *ngIf="userForm.get('confirmPassword')?.invalid">La confirmation du mot de passe est requise</span>
            <span *ngIf="userForm.hasError('passwordMismatch')">Les mots de passe ne correspondent pas</span>
          </div>
        </div>

        <!-- Password Change Fields (only for edit mode) -->
        <div class="form-group full-width" *ngIf="isEditMode">
          <label for="editNewPassword">Nouveau mot de passe (optionnel)</label>
          <input
            id="editNewPassword"
            type="password"
            formControlName="newPassword"
            placeholder="Laissez vide pour ne pas changer"
          />
        </div>
      </div>

      <div class="form-actions">
        <button type="button" class="btn-cancel" (click)="closeUserForm()">
          Annuler
        </button>
        <button type="submit" class="btn-submit" [disabled]="!userForm.valid">
          {{ isEditMode ? "Enregistrer" : "Créer" }}
        </button>
      </div>
    </form>
  </div>

</div>
    <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
