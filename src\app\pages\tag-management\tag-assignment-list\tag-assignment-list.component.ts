import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { TagAssignmentApiService } from '@app/core/services/administrative/tag-assignment.service';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { TagAssignmentComponent } from '../tag-assignment/tag-assignment.component';
import { SuccessComponent } from '../../../shared/components/success/success/success.component';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { GenericTableClientComponent } from '@app/components/generic-table-client/generic-table-client.component';

@Component({
  selector: 'app-tag-assignment-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    GenericTableClientComponent,
    MatPaginatorModule,
    SuccessComponent,
    MatDialogModule,
    MatButtonModule,
    NgToastComponent,
    MatIconModule,
    NgxUiLoaderModule,
    TagAssignmentComponent
  ],
  templateUrl: './tag-assignment-list.component.html',
  styleUrls: ['./tag-assignment-list.component.css']
})
export class TagAssignmentListComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;
  @ViewChild('SuccessComponent') successComponent!: SuccessComponent;

  // Data
  assignments: any[] = [];
  allAssignments: any[] = []; // Store all data for filtering
  filteredAssignments: any[] = []; // Store filtered data

  // Pagination
  currentPage = 0;
  pageSize = 5;
  totalCount = 0;

  // UI State
  isLoading = false;
  searchTerm = '';

  // Table configuration for assignments
  headers: { header: string; colspan: number }[] = [
    { header: 'Tag', colspan: 1 },
    { header: 'Type de Cible', colspan: 1 },
    { header: 'Cible', colspan: 1 },
  ];

  keys: string[] = [
    'TagName',
    'TargetTypeDisplay',
    'TargetName',
  ];

  constructor(
    readonly toast: NgToastService,
    readonly dialog: MatDialog,
    readonly tagAssignmentService: TagAssignmentApiService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  ngOnInit(): void {
    this.loadAssignments();
  }

  async loadAssignments(): Promise<void> {
    this.ngxUiLoaderService.start();
    this.isLoading = true;
    try {
      const rawAssignments = await this.tagAssignmentService.getAssignmentView().toPromise() || [];

      // Transform assignments for table display and store all data
      this.allAssignments = rawAssignments.map((assignment: any) => ({
        ...assignment,
        TagName: assignment.Nom, // Tag name from the view
        TargetName: assignment.TargetName, // Target name from the view
        TargetTypeDisplay: assignment.TargetType, // Target type from the view (already as string)
      }));

      // Apply filtering and pagination
      this.applyFiltersAndPagination();
    } catch (error) {
      console.error('Error loading assignments:', error);
      this.showError('Erreur lors du chargement des affectations');
    } finally {
      this.isLoading = false;
      this.ngxUiLoaderService.stop();
    }
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.applyFiltersAndPagination();
  }

  onSearchInput(): void {
    // Clear search results when input is manually cleared
    if (!this.searchTerm.trim()) {
      this.currentPage = 0;
      this.applyFiltersAndPagination();
    }
  }

  filterAssignments(): void {
    this.currentPage = 0; // Reset to first page when searching
    this.applyFiltersAndPagination();
  }

  private applyFiltersAndPagination(): void {
    // Apply search filter
    this.filteredAssignments = this.allAssignments.filter(assignment => {
      if (!this.searchTerm.trim()) {
        return true; // Show all if no search term
      }

      const searchLower = this.searchTerm.toLowerCase();
      return (
        assignment.TagName?.toLowerCase().includes(searchLower) ||
        assignment.TargetName?.toLowerCase().includes(searchLower) ||
        assignment.TargetTypeDisplay?.toLowerCase().includes(searchLower)
      );
    });

    // Update total count
    this.totalCount = this.filteredAssignments.length;

    // Apply pagination
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.assignments = this.filteredAssignments.slice(startIndex, endIndex);
  }



  openTagAssignmentDialog(): void {
    const dialogRef = this.dialog.open(TagAssignmentComponent, {
      width: '600px',
      maxWidth: '90vw',
      maxHeight: '90vh',
      disableClose: false,
      panelClass: 'tag-assignment-dialog'
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Reload assignments if a new assignment was created
        this.loadAssignments();
      }
    });
  }

  handleAction(event: { action: string; row: any }): void {
    switch (event.action) {
      case 'delete':
        this.deleteAssignment(event.row);
        break;
      default:
        console.warn('Unknown action:', event.action);
    }
  }

  deleteAssignment(assignment: any): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmer la suppression',
        message: 'Êtes-vous sûr de vouloir supprimer cette affectation ?',
        icon: 'delete',
      },
    });

    dialogRef.afterClosed().subscribe(async (result) => {
      if (result && assignment.Id) {
        this.isLoading = true;

        try {
          await this.tagAssignmentService.delete(assignment.Id).toPromise();
          this.showSuccess('Affectation supprimée avec succès');
          await this.loadAssignments();
        } catch (error) {
          console.error('Error deleting assignment:', error);
          this.showError('Erreur lors de la suppression de l\'affectation');
        } finally {
          this.isLoading = false;
        }
      }
    });
  }





  public showSuccess(message: string): void {
    this.toast.info(message, 'Succès', 3000, false);
  }

  public showError(message: string): void {
    this.toast.warning(message, 'Erreur', 3000, false);
  }
}
