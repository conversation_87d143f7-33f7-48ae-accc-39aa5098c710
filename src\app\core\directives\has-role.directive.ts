import { Directive, Input, TemplateRef, ViewContainerRef, OnInit, OnDestroy } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { RoleService } from '../services/role.service';

@Directive({
  selector: '[appHasRole]',
  standalone: true
})
export class HasRoleDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private hasView = false;

  @Input() set appHasRole(roles: string | string[]) {
    this.checkRoles(roles);
  }

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private roleService: RoleService
  ) {}

  ngOnInit(): void {
    // Initial check when directive is initialized
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private checkRoles(roles: string | string[]): void {
    const roleArray = Array.isArray(roles) ? roles : [roles];
    const hasRequiredRole = this.roleService.hasAnyRole(roleArray);

    if (hasRequiredRole && !this.hasView) {
      // User has required role and view is not shown - show it
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    } else if (!hasRequiredRole && this.hasView) {
      // User doesn't have required role and view is shown - hide it
      this.viewContainer.clear();
      this.hasView = false;
    }
  }
}

/**
 * Usage examples:
 * 
 * <!-- Show only for admins -->
 * <div *appHasRole="'ADMIN'">Admin only content</div>
 * 
 * <!-- Show for multiple roles -->
 * <div *appHasRole="['ADMIN', 'SUPERADMIN']">Admin content</div>
 * 
 * <!-- Show for clients -->
 * <div *appHasRole="'CLIENT'">Client only content</div>
 * 
 * <!-- Show for any authenticated user -->
 * <div *appHasRole="['ADMIN', 'CLIENT', 'INTEGRATEUR']">All users content</div>
 */
