<ngx-loading 
  [show]="isLoading" 
  [config]="{ 
    animationType: 'threeBounce', 
    backdropBackgroundColour: 'rgba(0,0,0,0.2)', 
    primaryColour: '#10b981',
    secondaryColour: '#10b981',
    tertiaryColour: '#10b981'
  }"></ngx-loading>

<div class="enhanced-container">
  <!-- Header Section -->
  <div class="enhanced-header">
    <div class="header-content">
      <h1 class="enhanced-title">Gestion des Abonnements</h1>
      <p class="enhanced-subtitle">
        Gérez efficacement tous vos abonnements clients avec notre interface moderne
      </p>
    </div>
    <div class="header-actions">
      <button class="add-subscription-btn" (click)="goToAbonementNew()">
        <span class="material-icons">add_circle</span>
        Nouvel Abonnement
      </button>
    </div>
  </div>

  <!-- Statistics Dashboard -->
  <div class="stats-dashboard">
    <div class="stat-card stat-total" @fadeInOut>
      <div class="stat-icon-wrapper">
        <span class="material-icons stat-icon">assessment</span>
      </div>
      <div class="stat-content">
        <h3 class="stat-number">{{ getTotalSubscriptionsCount() }}</h3>
        <p class="stat-label">Total Abonnements</p>
      </div>
    </div>
    
    <div class="stat-card stat-pending" @fadeInOut>
      <div class="stat-icon-wrapper">
        <span class="material-icons stat-icon">schedule</span>
      </div>
      <div class="stat-content">
        <h3 class="stat-number">{{ getPendingSubscriptionsCount() }}</h3>
        <p class="stat-label">En Attente</p>
      </div>
    </div>
    
    <div class="stat-card stat-paid" @fadeInOut>
      <div class="stat-icon-wrapper">
        <span class="material-icons stat-icon">check_circle</span>
      </div>
      <div class="stat-content">
        <h3 class="stat-number">{{ getPaidSubscriptionsCount() }}</h3>
        <p class="stat-label">Payés</p>
      </div>
    </div>
    
    <div class="stat-card stat-cancelled" @fadeInOut>
      <div class="stat-icon-wrapper">
        <span class="material-icons stat-icon">cancel</span>
      </div>
      <div class="stat-content">
        <h3 class="stat-number">{{ getCancelledSubscriptionsCount() }}</h3>
        <p class="stat-label">Résiliés</p>
      </div>
    </div>
  </div>

  <!-- Enhanced Filters Section -->
  <div class="enhanced-filters-section" @slideDown>
    <div class="filters-header">
      <h3 class="filters-title">
        <span class="material-icons">filter_list</span>
        Filtres & Recherche
      </h3>
      <button class="clear-filters-btn" (click)="clearFilters()" *ngIf="currentSearchTerm || selectedStatusFilter || selectedLicenceFilter || selectedFrequencyFilter">
        <span class="material-icons">clear_all</span>
        Effacer tous les filtres
      </button>
    </div>
    
    <div class="filters-content">
      <div class="search-section">
        <div class="enhanced-search-input" style="display: flex; align-items: center; gap: 0.5rem;">
          <span class="material-icons search-icon">search</span>
          <input
            type="text"
            class="search-input"
            [(ngModel)]="searchQuery"
            (keyup.enter)="performSearch()"
            placeholder="Rechercher par client, licence ou statut..."
            autocomplete="off"
            style="flex: 1;"
          />
          <button class="search-btn" (click)="performSearch()" [disabled]="!searchQuery.trim()">
            <span class="material-icons">search</span>
          </button>
          <button class="clear-search-btn" (click)="clearSearch()" *ngIf="searchQuery">
            <span class="material-icons">close</span>
          </button>
        </div>
      </div>
      
      <div class="filter-controls">
        <div class="filter-item">
          <label class="filter-label">
            Statut
          </label>
          <select [(ngModel)]="selectedStatusFilter" (change)="onFilterChange()" class="enhanced-select">
            <option value="">Tous les statuts</option>
            <option value="Payé">Payé</option>
            <option value="En attente">En attente</option>
            <option value="Résilié">Résilié</option>
          </select>
        </div>
        
        <div class="filter-item">
          <label class="filter-label">
            Licence
          </label>
          <select [(ngModel)]="selectedLicenceFilter" (change)="onFilterChange()" class="enhanced-select">
            <option [ngValue]="''">Toutes les licences</option>
            <option *ngFor="let licence of availableLicences" [ngValue]="licence">{{ licence }}</option>
          </select>
        </div>
        
        <div class="filter-item">
          <label class="filter-label">
            Fréquence
          </label>
          <select [(ngModel)]="selectedFrequencyFilter" (change)="onFilterChange()" class="enhanced-select">
            <option value="">Toutes les fréquences</option>
            <option value="Mensuel">Mensuel</option>
            <option value="Annuel">Annuel</option>
          </select>
        </div>
      </div>
    </div>
  </div>

  <!-- View Mode Toggle Section -->
  <div class="view-mode-section" @fadeInOut>
    <div class="view-mode-toggle">
      <button class="view-toggle-btn" 
              [class.active]="viewMode === 'cards'"
              (click)="toggleViewMode('cards')">
        <span class="material-icons">view_module</span>
        <span>Vue Cartes</span>
      </button>
      <button class="view-toggle-btn" 
              [class.active]="viewMode === 'table'"
              (click)="toggleViewMode('table')">
        <span class="material-icons">table_rows</span>
        <span>Vue Tableau</span>
      </button>
    </div>
  </div>

  <!-- Enhanced Subscription Cards/Table Grid -->
  <div class="subscriptions-grid" @fadeInOut>
    <div class="grid-header">
      <h3 class="grid-title">
        <span class="material-icons">{{ viewMode === 'cards' ? 'view_module' : 'table_rows' }}</span>
        Abonnements ({{ tableTotalElements }})
      </h3>
      <div class="page-size-selector">
        <label>Afficher:</label>
        <select [(ngModel)]="tablePageSize" (change)="onTablePageSizeChange()" class="page-size-select">
          <option value="6">6</option>
          <option value="12">12</option>
          <option value="24">24</option>
          <option value="48">48</option>
        </select>
        <span>par page</span>
      </div>
    </div>
    
    <!-- Cards View -->
    <div class="cards-container" 
         *ngIf="viewMode === 'cards' && filteredTableRows.length > 0" 
         @tableSlide>
      <div class="subscription-card" 
           *ngFor="let row of filteredTableRows; trackBy: trackBySubscriptionId"
           @cardSlide>
        
        <!-- Card Header -->
        <div class="card-header">
          <div class="client-info">
            <div class="client-avatar">
              <img *ngIf="row.ClientLogo" 
                   [src]="'data:image/png;base64,' + row.ClientLogo"
                   [alt]="row.ClientName"
                   class="client-image">
              <div class="client-fallback" *ngIf="!row.ClientLogo">
                {{ getClientInitials(row.ClientName) }}
              </div>
            </div>
            <div class="client-details">
              <h4 class="client-name">{{ row.ClientName }}</h4>
              <p class="licence-name">{{ row.LicenceName }}</p>
            </div>
          </div>
        </div>

        <!-- Status Badge -->
        <div class="status-section">
          <div class="status-badge" [ngClass]="getStatusBadgeClass(row.Status)">
            <span class="material-icons status-icon">{{ getStatusIcon(row.Status) }}</span>
            <span class="status-text">{{ row.Status }}</span>
          </div>
        </div>

        <!-- Card Content -->
        <div class="card-content">
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">
                <span class="material-icons">event</span>
                Date début
              </span>
              <span class="info-value" style="margin-left: 1.5rem">{{ row.DateDebut }}</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">
                <span class="material-icons">event_busy</span>
                Date fin
              </span>
              <span class="info-value" style="margin-left: 1.5rem">{{ row.DateFin }}</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">
                <span class="material-icons">euro</span>
                Prix
              </span>
              <span class="info-value price-value" style="margin-left: 1.5rem">{{ formatCurrency(row.price) }}</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">
                <span class="material-icons">repeat</span>
                Fréquence
              </span>
              <span class="info-value" style="margin-left: 1.5rem">{{ row.PaymentFrequency }}</span>
            </div>
          </div>
        </div>

        <!-- Card Footer -->
        <div class="card-footer">
          <div class="card-actions" *ngIf="row.showActions">
            <button class="card-action-btn modify-btn" (click)="onTableAction('Modifier', row)">
              <span class="material-icons">edit</span>
              Modifier
            </button>
            <button class="card-action-btn modify-btn cancel-btn" (click)="onTableAction('Annuler', row)">
              <span class="material-icons">cancel</span>
              Annuler
            </button>
          </div>
          <div class="card-no-actions" *ngIf="!row.showActions">
            Abonnement résilié
          </div>
        </div>
      </div>
    </div>

    <!-- Table View -->
    <div *ngIf="viewMode === 'table' && filteredTableRows.length > 0" 
         @tableSlide>
      <!-- Table Strip -->
      <div class="table-strip"></div>
      
      <div class="table-container" 
           #tableContainer
           (touchstart)="onTouchStart($event)"
           (touchmove)="onTouchMove($event)"
           (touchend)="onTouchEnd($event)">
        
        <table class="enhanced-table" #resizableTable>
          <thead>
            <tr>
              <th>
                Client
                <div class="column-resizer" 
                     (mousedown)="startResize($event, 0)"
                     [class.resizing]="resizingColumn === 0"></div>
              </th>
              <th>
                Licence
                <div class="column-resizer" 
                     (mousedown)="startResize($event, 1)"
                     [class.resizing]="resizingColumn === 1"></div>
              </th>
              <th>
                Date Début
                <div class="column-resizer" 
                     (mousedown)="startResize($event, 2)"
                     [class.resizing]="resizingColumn === 2"></div>
              </th>
              <th>
                Date Fin
                <div class="column-resizer" 
                     (mousedown)="startResize($event, 3)"
                     [class.resizing]="resizingColumn === 3"></div>
              </th>
              <th>
                Prix
                <div class="column-resizer" 
                     (mousedown)="startResize($event, 4)"
                     [class.resizing]="resizingColumn === 4"></div>
              </th>
              <th>
                Statut
                <div class="column-resizer" 
                     (mousedown)="startResize($event, 5)"
                     [class.resizing]="resizingColumn === 5"></div>
              </th>
              <th>
                Fréquence
                <div class="column-resizer" 
                     (mousedown)="startResize($event, 6)"
                     [class.resizing]="resizingColumn === 6"></div>
              </th>
              <th class="actions-cell">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of filteredTableRows; trackBy: trackBySubscriptionId">
              <td>
                <span class="table-client-name">{{ row.ClientName }}</span>
              </td>
              <td>
                <span class="table-licence-name">{{ row.LicenceName }}</span>
              </td>
              <td>
                <span class="table-date">{{ row.DateDebut }}</span>
              </td>
              <td>
                <span class="table-date">{{ row.DateFin }}</span>
              </td>
              <td>
                <span class="table-price">{{ formatCurrency(row.price) }}</span>
              </td>
              <td>
                <span class="table-status-badge" [ngClass]="getStatusBadgeClass(row.Status)">
                  {{ row.Status }}
                </span>
              </td>
              <td>
                <span class="table-frequency">{{ row.PaymentFrequency }}</span>
              </td>
              <td class="actions-cell">
                <div class="action-dropdown" *ngIf="row.showActions">
                  <button class="table-action-btn modify-btn" 
                          (click)="onTableAction('Modifier', row)">
                    <span class="material-icons">edit</span>
                  </button>
                  <button class="table-action-btn modify-btn cancel-btn" 
                          (click)="onTableAction('Annuler', row)">
                    <span class="material-icons">cancel</span>
                  </button>
                </div>
                <div *ngIf="!row.showActions" class="action-dropdown">
                  <span class="table-frequency" style="color: #94a3b8; font-size: 0.75rem;">Résilié</span>
                </div>
              </td>
            </tr>
            <tr *ngIf="filteredTableRows.length === 0 && !isLoading">
              <td colspan="8" class="no-results">
                Aucun abonnement trouvé
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- No Data Template -->
    <div class="no-data-container" 
         *ngIf="filteredTableRows.length === 0" 
         @fadeInOut>
      <div class="no-data-content">
        <span class="material-icons no-data-icon">inbox</span>
        <h3 class="no-data-title">Aucun abonnement trouvé</h3>
        <p class="no-data-message">
          Aucun abonnement ne correspond à vos critères de recherche.
          Essayez de modifier vos filtres ou créez un nouvel abonnement.
        </p>
        <button class="create-subscription-btn" (click)="goToAbonementNew()">
          <span class="material-icons">add</span>
          Créer un abonnement
        </button>
      </div>
    </div>
  </div>

  <!-- FIXED: Enhanced Pagination - Always show when total pages > 1 -->
  <div class="enhanced-pagination" *ngIf="tableTotalPages > 1" @slideDown>
    <div class="pagination-info">
      <span class="pagination-text">
        Affichage de {{ tableFirstItem }} à {{ tableLastItem }} sur {{ tableTotalElements }} éléments
        (Page {{ tableCurrentPage + 1 }} sur {{ tableTotalPages }})
      </span>
    </div>
    
    <div class="pagination-controls">
      <button class="pagination-btn prev-btn" 
              (click)="prevTablePage()" 
              [disabled]="tableCurrentPage === 0"
              [title]="'Page précédente'">
        <span class="material-icons">chevron_left</span>
      </button>
      
      <div class="page-numbers">
        <button *ngFor="let page of getVisiblePageNumbers()" 
                class="page-btn"
                [class.active]="page === tableCurrentPage"
                [class.ellipsis]="page === -1"
                [disabled]="page === -1"
                (click)="page !== -1 && goToTablePage(page)">
          {{ page === -1 ? '...' : (page + 1) }}
        </button>
      </div>
      
      <button class="pagination-btn next-btn" 
              (click)="nextTablePage()" 
              [disabled]="tableCurrentPage >= tableTotalPages - 1"
              [title]="'Page suivante'">
        <span class="material-icons">chevron_right</span>
      </button>
    </div>
  </div>

  <!-- Debug Info Panel - Enable by changing *ngIf="false" to *ngIf="true" for debugging -->
  <!-- <div class="debug-panel" *ngIf="true" style="position: fixed; bottom: 10px; right: 10px; background: rgba(0,0,0,0.9); color: white; padding: 15px; border-radius: 8px; font-size: 12px; z-index: 9999; max-width: 300px;">
    <h4 style="margin: 0 0 10px 0; color: #10b981;">🐛 Debug Info</h4>
    <div><strong>Total Elements:</strong> {{ tableTotalElements }}</div>
    <div><strong>Page Size:</strong> {{ tablePageSize }}</div>
    <div><strong>Current Page:</strong> {{ tableCurrentPage + 1 }}</div>
    <div><strong>Total Pages:</strong> {{ tableTotalPages }}</div>
    <div><strong>Has Pagination:</strong> {{ hasPagination ? 'Yes' : 'No' }}</div>
    <div><strong>Search Term:</strong> "{{ currentSearchTerm }}"</div>
    <div><strong>Status Filter:</strong> "{{ selectedStatusFilter }}"</div>
    <div><strong>Licence Filter:</strong> "{{ selectedLicenceFilter }}"</div>
    <div><strong>Frequency Filter:</strong> "{{ selectedFrequencyFilter }}"</div>
    <div><strong>Available Statuses:</strong> {{ availableStatuses.length }} - {{ availableStatuses.join(', ') }}</div>
    <div><strong>Available Licences:</strong> {{ availableLicences.length }}</div>
    <div><strong>Available Frequencies:</strong> {{ availableFrequencies.length }}</div>
    <div><strong>Loaded Subscriptions:</strong> {{ subscriptions.length }}</div>
    <div><strong>Table Rows:</strong> {{ filteredTableRows.length }}</div>
    <div style="margin-top: 10px;">
      <button (click)="testStatusFilter('Résilié')" style="background: #ef4444; color: white; border: none; padding: 4px 8px; margin: 2px; border-radius: 4px; cursor: pointer;">Test Résilié</button>
      <button (click)="testStatusFilter('En attente')" style="background: #f59e0b; color: white; border: none; padding: 4px 8px; margin: 2px; border-radius: 4px; cursor: pointer;">Test En attente</button>
      <button (click)="testStatusFilter('Payé')" style="background: #10b981; color: white; border: none; padding: 4px 8px; margin: 2px; border-radius: 4px; cursor: pointer;">Test Payé</button>
    </div>
  </div>
</div> -->

<!-- Popups and Modals -->
<div class="overlay" *ngIf="showCannotCancelPendingPopup" @fadeInOut>
  <div class="enhanced-popup" @slideDown>
    <div class="popup-header">
      <div class="popup-title">
        <span class="material-icons popup-icon error-icon">block</span>
        <h3>Annulation impossible</h3>
      </div>
      <button class="close-popup-btn" (click)="closeCannotCancelPendingPopup()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="popup-message">
        <p>Vous ne pouvez pas annuler un abonnement dont le statut est <strong>En attente</strong>.</p>
        <p class="popup-hint">Veuillez attendre que le statut change ou contactez l'administrateur.</p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="popup-btn primary" (click)="closeCannotCancelPendingPopup()">
        <span class="material-icons">check</span>
        Compris
      </button>
    </div>
  </div>
</div>

<div class="overlay" *ngIf="showCannotModifyResiliePopup" @fadeInOut>
  <div class="enhanced-popup" @slideDown>
    <div class="popup-header">
      <div class="popup-title">
        <span class="material-icons popup-icon error-icon">block</span>
        <h3>Modification impossible</h3>
      </div>
      <button class="close-popup-btn" (click)="closeCannotModifyResiliePopup()">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="popup-message">
        <p>Vous ne pouvez pas modifier un abonnement dont le statut est <strong>Résilié</strong>.</p>
        <p class="popup-hint">Les abonnements résiliés sont en lecture seule.</p>
      </div>
    </div>
    <div class="popup-actions">
      <button class="popup-btn primary" (click)="closeCannotModifyResiliePopup()">
        <span class="material-icons">check</span>
        Compris
      </button>
    </div>
  </div>
</div>

<!-- Success Notification -->
<div class="success-notification" *ngIf="showCancelNotification" @slideDown>
  <div style="position: absolute; top: 1rem; right: 1rem;">
    <button class="close-notification-btn" (click)="showCancelNotification = false">
      <span class="material-icons">close</span>
    </button>
  </div>
  <div class="notification-content" style="display: flex; align-items: flex-start; gap: 1.5rem; position: relative;">
    <span class="material-icons success-icon" style="color: var(--installation);">check_circle</span>
    <div class="notification-text">
      <h4>Abonnement résilié avec succès !</h4>
      <p>L'abonnement a été résilié et le client en sera notifié.</p>
    </div>
  </div>
</div>

<!-- Confirmation Popup for Resiliation -->
<div class="overlay" *ngIf="showCancelPopup" @fadeInOut>
  <div class="enhanced-popup" @slideDown>
    <div class="popup-header">
      <div class="popup-title">
        <span class="material-icons popup-icon error-icon" style="color: #ef4444;">warning</span>
        <h3>Confirmer la résiliation</h3>
      </div>
      <button class="close-popup-btn" (click)="showCancelPopup = false">
        <span class="material-icons">close</span>
      </button>
    </div>
    <div class="popup-content">
      <div class="popup-message">
        <p>
          Êtes-vous sûr de vouloir résilier l'abonnement ?
        </p>
        <div class="client-license-summary">
          <div class="summary-item">
            <span class="label">Client :</span>
            <span class="client-info-summary">{{ cancelClientName }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Licence :</span>
            <span class="license-name">{{ cancelLicenceName }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="popup-actions">
      <button class="popup-btn" (click)="showCancelPopup = false">
        Annuler
      </button>
      <button class="popup-btn primary" (click)="confirmCancelSubscription()">
        Oui, résilier l'abonnement
      </button>
    </div>
  </div>
</div>