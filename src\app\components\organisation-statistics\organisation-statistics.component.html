<!-- Organisation Statistics Component -->
<div class="statistics-container" *ngIf="!isLoading">
  <!-- Header -->
  <div class="statistics-header">
    <div class="header-top">
      <button class="back-button" (click)="goBack()" title="Retour">
        <i class="material-icons">arrow_back</i>
        Retour
      </button>
    </div>
    <h1 class="statistics-title">
      <i class="material-icons title-icon">analytics</i>
      Statistiques - {{ organisation?.Nom || 'Organisation' }}
    </h1>
    <p class="statistics-subtitle" *ngIf="organisation">
      Aperçu détaillé des performances et métriques de l'organisation
    </p>
  </div>

  <!-- Statistics Cards Grid -->
  <div class="statistics-grid">
    <div class="stat-card" *ngFor="let stat of statistics">
      <div class="card-header">
        <div class="card-title">{{ stat.title }}</div>
        <div class="card-icon">
          <i class="material-icons" [style.color]="stat.iconColor">{{ stat.icon }}</i>
        </div>
      </div>
      
      <div class="card-value">
        <span class="amount-text">{{ stat.value }}</span>
        <span class="unit" *ngIf="stat.unit">{{ stat.unit }}</span>
      </div>
      
      <div class="card-footer">
        <div class="percentage" [ngClass]="getTrendClass(stat.trend)" *ngIf="stat.percentage !== undefined">
          <i class="material-icons trend-icon">{{ getTrendIcon(stat.trend) }}</i>
          <span>{{ stat.percentage }}%</span>
        </div>
        <div class="na-badge" *ngIf="stat.percentage === undefined">
          N/A
        </div>
      </div>
    </div>
  </div>

  <!-- Summary Section -->
  <div class="summary-section" *ngIf="organisation && clients.length > 0">
    <div class="summary-card">
      <h3 class="summary-title">
        <i class="material-icons">summarize</i>
        Résumé de l'organisation
      </h3>
      <div class="summary-content">
        <div class="summary-item">
          <span class="summary-label">Organisation:</span>
          <span class="summary-value">{{ organisation.Nom }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Nombre de clients:</span>
          <span class="summary-value">{{ clients.length }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Secteur d'activité:</span>
          <span class="summary-value">{{ getBusinessSectors() }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Statut global:</span>
          <span class="summary-value status-active">Actif</span>
        </div>
      </div>
    </div>
  </div>

  <!-- No Data Message -->
  <div class="no-data" *ngIf="clients.length === 0">
    <i class="material-icons no-data-icon">info</i>
    <h3>Aucune donnée disponible</h3>
    <p>Cette organisation n'a pas encore de clients associés.</p>
  </div>
</div>

<!-- Loading State -->
<div class="loading-container" *ngIf="isLoading">
  <div class="loading-spinner">
    <i class="material-icons spinning">refresh</i>
  </div>
  <p class="loading-text">Chargement des statistiques...</p>
</div>