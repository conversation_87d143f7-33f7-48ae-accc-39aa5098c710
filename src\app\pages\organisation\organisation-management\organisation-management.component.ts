import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormBuilder } from '@angular/forms';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { Client, ClientWithSiteStatus } from '@app/core/models/client';
import { Organisation } from '@app/core/models/organisation';
import { OrganisationApiService } from '@app/core/services/administrative/organisation.service';
import {
  FilterParam,
  Lister,
  Pagination,
  SortPage,
} from '@app/core/models/util/page';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { SuccessComponent } from '../../../shared/components/success/success/success.component';
import { FormaValidationService } from '@app/shared/services/forma-validation.service';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { MatIconModule } from '@angular/material/icon';
import { ClientFormComponent } from '../client-form/client-form.component';
import { MatButtonModule } from '@angular/material/button';
import { ClientFormService } from '@app/shared/services/client-form.service';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { GenericTableClientComponent } from '@app/components/generic-table-client/generic-table-client.component';
import { ClientWithSiteStatusView } from '@app/shared/models/clientWithSiteStatusView';

// Define the interface for the API request
@Component({
  selector: 'app-organisation-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableClientComponent,
    MatPaginatorModule,
    SuccessComponent,
    MatDialogModule,
    MatButtonModule,
    NgToastComponent,
    MatIconModule,
    NgxUiLoaderModule,
  ],
  templateUrl: './organisation-management.component.html',
  styleUrls: ['./organisation-management.component.css'],
  animations: [
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition(':enter', [animate('300ms ease-out', style({ opacity: 1 }))]),
    ]),
  ],
})
export class OrganisationManagementComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;
  clientData: Client = new Client();
  @ViewChild('SuccessComponent') successComponent!: SuccessComponent;
  clients: ClientWithSiteStatusView[] = [];
  clientWithSiteStatus: ClientWithSiteStatus[] = [];
  filteredClients: Client[] = [];
  showErrorMessages: string[] = [];
  organisations: Organisation[] = [];
  pageSize: number = 5;
  currentPage: number = 0;
  totalClients: number = 0;
  uploadedLogo: File | undefined;
  showMoreFields: boolean = false;
  base64Image: string = '';
  totalCount: number = 0;
  request: Lister = {};
  selectedStatus: string = '';
  availableStatuses: string[] = ['Actif', 'Inactif', 'En maintenance'];
  statusChange: string = '';

  constructor(
    readonly clientApiService: ClientApiService,
    readonly organisationApiService: OrganisationApiService,
    readonly fb: FormBuilder,
    readonly router: Router,
    readonly route: ActivatedRoute,
    private clientFormService: ClientFormService,
    private dialog: MatDialog,
    private toast: NgToastService,
    private ngxUiLoaderService: NgxUiLoaderService
  ) {}

  // Table configuration for clients
  headers: { header: string; colspan: number }[] = [
    { header: 'Raison Sociale', colspan: 1 },
    { header: 'Email', colspan: 1 },
    { header: 'Téléphone', colspan: 1 },
    { header: 'Organisation', colspan: 1 },
    { header: 'RC', colspan: 1 },
    { header: 'IF', colspan: 1 },
    { header: 'Statut', colspan: 1 },
    { header: 'Sites Actif', colspan: 1 },
    { header: 'Sites Inactif', colspan: 1 },
    { header: 'Sites En Maintenance', colspan: 1 },
    { header: 'Sites En Installation', colspan: 1 },
  ];

  keys: string[] = [
    'Name',
    'ContactEmail',
    'Phone',
    'Organisation',
    'RC',
    'IF',
    'ClientStatus',
    'SiteActif',
    'SiteInactif',
    'SiteEnMaintenance',
    'SiteEnInstallation',
  ];

  isLoading: boolean = false;
  searchTerm: string = '';
  showCreateForm: boolean = false;

  ngOnInit(): void {
    this.loadOrganisations();
    this.loadClients();
  }
  loadOrganisations(): void {
    this.isLoading = true;
    this.organisationApiService.getAll().subscribe({
      next: (organisations: Organisation[]) => {
        this.organisations = organisations;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading organisations:', error);
        this.isLoading = false;
      },
    });
  }

  applyStatus(event: EventTarget | null): void {
    const selectElement = event as HTMLSelectElement;
    this.statusChange = selectElement.value;
    this.loadClients();
  }

  loadClients(): void {
    // this.clientApiService.getClients().subscribe({
    //   next: (data: Client[]) => {
    //     this.clients = data;
    //     this.filteredClients = [...this.clients];
    //     this.extractStatuses();
    //   },
    //   error: (err) => {
    //     console.error('Error loading clients:', err);
    //   },
    // });

    this.ngxUiLoaderService.start();
    if (this.request.Pagination === undefined) {
      const pagination: Pagination = {
        CurrentPage: this.currentPage + 1,
        PageSize: this.pageSize,
      };
      this.request.Pagination = pagination;
    } else {
      this.request.Pagination.CurrentPage = this.currentPage + 1;
      this.request.Pagination.PageSize = this.pageSize;
    }

    const FilterParams: FilterParam[] = [];
    const SortParams: SortPage[] = [
      {
        Column: 'Name',
        Sort: 'asc',
      },
    ];

    this.request.SortParams = [{ Column: 'Name', Sort: 'asc' }];
    if (this.searchTerm.trim().length > 0) {
      console.log(this.searchTerm);
      this.keys.forEach((k) => {
        if (
          k != 'SiteActif' &&
          k != 'SiteInactif' &&
          k != 'SiteEnMaintenance' &&
          k != 'SiteEnInstallation'
        ) {
          FilterParams.push({
            Column: k,
            Op: 'contains',
            Value: this.searchTerm,
            AndOr: 'OR',
          });
        }
      });
    }
    // Ajout du filtre de statut
    if (this.statusChange) {
      FilterParams.push({
        Column: 'ClientStatus',
        Op: 'eq',
        Value: this.statusChange,
        AndOr: 'AND',
      });
    }
    this.request.FilterParams = FilterParams;

    this.isLoading = true;
    console.log('Request for paginated clients:', this.request);
    this.clientApiService.getPageSiteStatus(this.request).subscribe({
      next: (result: any) => {
        this.request = result.Lister;
        if (result?.Lister?.pagination?.totalElement !== undefined) {
          this.totalCount = result.Lister.pagination.totalElement;
        } else if (result?.Lister?.Pagination?.TotalElement !== undefined) {
          this.totalCount = result.Lister.Pagination.TotalElement;
        } else {
          this.totalCount = Math.max(8, result.Content.length);
        }
        this.clients = result.Content ?? [];
        console.log('Clients loaded:', this.clients);
        // this.filterClients();
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
        this.request = result.Lister;
      },
      error: (error) => {
        this.showError('Error loading paginated clients:', 'Erreur');
        console.log('Error loading paginated clients:', error);
        this.isLoading = false;
        this.ngxUiLoaderService.stop();
      },
    });
  }
  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadClients();
  }

  onSearchChange(): void {
    this.loadClients();
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.loadClients();
  }

  showClientForm(): void {
    const dialogRef = this.clientFormService
      .openCreateClientDialog(this.organisations)
      .then((result) => {
        this.viewClientDetails(result?.Id || null);
      });
  }

  handleAction(event: { action: string; row: ClientWithSiteStatusView }): void {
    switch (event.action) {
      case 'view':
        this.viewClientDetails(event.row.ClientId ?? '');
        break;
      case 'edit':
        this.editClient(event.row);
        break;
      case 'delete':
        this.deleteClient(event.row);
        break;
      // case 'toggle':
      //   this.toggleClientActivation(event.row);
      //   break;
      default:
        console.warn('Unknown action:', event.action);
    }
  }

  viewClientDetails(client: string | null): void {
    this.router.navigate(['organisation-details/', client]);
  }

  editClient(client: ClientWithSiteStatusView): void {
    this.clientApiService.getById(client.ClientId ?? '').subscribe({
      next: (clientData: Client) => {
        // Open the edit dialog with the fetched client data
        const dialogRef = this.clientFormService
          .openEditClientDialog(clientData, this.organisations)
          .then((result) => {
            if (result) {
              this.loadClients();
            }
          });
      },
      error: (error) => {
        console.error('Error fetching client details:', error);
        this.showError(
          'Erreur lors de la récupération des détails du client',
          'Erreur'
        );
      },
    });
    // const dialogRef = this.clientFormService
    //   .openEditClientDialog(client, this.organisations)
    //   .then((result) => {
    //     if (result) {
    //       this.loadClients();
    //     }
    //   });
  }

  deleteClient(client: ClientWithSiteStatusView): void {
    if (!client.ClientId) {
      this.showError(
        'Erreur lors de la suppression de client, Essayer ultérieurement',
        'Erreur'
      );
      return;
    }

    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmation de suppression',
        message:
          `Êtes-vous sûr de vouloir supprimer définitivement le client ${client.Name} ?\n\n` +
          `Cette action supprimera toutes les données associées et est irréversible.`,
        icon: 'warning',
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.isLoading = true;

        this.clientApiService.delete(client.ClientId).subscribe({
          next: () => {
            this.clients = this.clients.filter(
              (c) => c.ClientId !== client.ClientId
            );
            this.totalClients--;

            this.showSuccess(
              'Le Client a été suprimé avec succes',
              'Information'
            );
          },
          error: (error) => {
            this.showError(
              'Erreur lors de la suppression de client, Essayer ultérieurement',
              'Erreur'
            );
          },
          complete: () => {
            this.isLoading = false;
          },
        });
      }
    });
  }

  openDialog() {
    this.dialog.open(ClientFormComponent, {
      panelClass: 'custom-dialog',
      position: {
        top: '50%',
        bottom: '50%',
        left: '50%',
        right: '50%',
      },
      disableClose: true,
    });
  }

  public showSuccess(message: string, title: string) {
    this.toast.info(message, title, 3000, false);
  }

  public showError(message: string, title: string) {
    this.toast.warning(message, title, 3000, false);
  }
}
