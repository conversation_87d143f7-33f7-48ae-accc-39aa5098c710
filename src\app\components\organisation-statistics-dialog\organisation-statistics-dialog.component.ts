import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { OrganisationStatisticsComponent } from '../organisation-statistics/organisation-statistics.component';

@Component({
  selector: 'app-organisation-statistics-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, OrganisationStatisticsComponent],
  template: `
    <div class="dialog-container">
      <div class="dialog-header">
        <h2 mat-dialog-title>Statistiques de l'organisation</h2>
        <button 
          mat-dialog-close 
          class="close-button"
          aria-label="Fermer"
        >
          <i class="material-icons">close</i>
        </button>
      </div>
      
      <div mat-dialog-content class="dialog-content">
        <app-organisation-statistics [organisationId]="data.organisationId"></app-organisation-statistics>
      </div>
    </div>
  `,
  styles: [`
    .dialog-container {
      max-width: 1200px;
      width: 100%;
      max-height: 90vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px 0 24px;
      border-bottom: 1px solid #e2e8f0;
      margin-bottom: 0;
    }

    .dialog-header h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #2d3748;
    }

    .close-button {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #718096;
      transition: all 0.2s ease;
    }

    .close-button:hover {
      background-color: #f7fafc;
      color: #2d3748;
    }

    .close-button i {
      font-size: 20px;
    }

    .dialog-content {
      flex: 1;
      overflow-y: auto;
      padding: 0;
    }

    ::ng-deep .mat-mdc-dialog-container {
      padding: 0;
    }

    ::ng-deep .mat-mdc-dialog-surface {
      border-radius: 12px;
    }
  `]
})
export class OrganisationStatisticsDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<OrganisationStatisticsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { organisationId: string }
  ) {}
}